"use client";
import React, { useEffect, useRef, useState } from "react";
import Link from "next/link";
import { X, Compass, Plus, Search, Settings } from "lucide-react";
import CommunityIcon from "./communitynav/CommunityIcon";

interface Community {
  _id: string;
  name: string;
  slug: string;
  iconImageUrl?: string;
  role: string;
}

interface CommunitiesSideDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  userCommunities: Community[];
  currentCommunity: Community | null;
}

export default function CommunitiesSideDrawer({
  isOpen,
  onClose,
  userCommunities,
  currentCommunity,
}: CommunitiesSideDrawerProps) {
  const drawerRef = useRef<HTMLDivElement>(null);
  const [searchTerm, setSearchTerm] = useState("");

  // Debug logging
  console.log("CommunitiesSideDrawer: Received props:", {
    isOpen,
    userCommunitiesLength: userCommunities?.length || 0,
    userCommunities: userCommunities,
    currentCommunity: currentCommunity,
  });


  // Close drawer when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (drawerRef.current && !drawerRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      document.body.style.overflow = "hidden"; // Prevent background scroll
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  // Close drawer on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
    };
  }, [isOpen, onClose]);

  return (
    <>
      {/* Backdrop */}
      <div
        className={`fixed inset-0 bg-black bg-opacity-60 z-[9998] transition-opacity duration-300 ${
          isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        }`}
        onClick={onClose}
      />

      {/* Side Drawer */}
      <div
        ref={drawerRef}
        className={`fixed left-0 top-0 h-full w-80 max-w-[85vw] z-[9999] flex flex-col transform transition-transform duration-300 ease-in-out ${
          isOpen ? "translate-x-0" : "-translate-x-full"
        }`}
        style={{
          backgroundColor: "var(--bg-primary)",
          boxShadow: "4px 0 20px rgba(0, 0, 0, 0.3)",
          border: "1px solid var(--border-color)"
        }}
      >
        {/* Header */}
        <div 
          className="p-4 border-b"
          style={{ borderColor: "var(--border-color)" }}
        >
          <div className="flex items-center justify-between mb-4">
            <h2 
              className="text-lg font-semibold"
              style={{ color: "var(--text-primary)" }}
            >
              Communities
            </h2>
            <button
              type="button"
              onClick={onClose}
              className="p-2 rounded-lg transition-colors duration-200"
              style={{
                color: "var(--text-secondary)"
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = "var(--hover-bg)";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = "transparent";
              }}
              aria-label="Close communities menu"
            >
              <X size={20} />
            </button>
          </div>
          <div className="relative">
            <Search 
              size={20} 
              className="absolute left-3 top-1/2 transform -translate-y-1/2" 
              style={{ color: "var(--text-muted)" }}
            />
            <input
              type="text"
              placeholder="Search"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-10 py-2 rounded-lg border-0 focus:outline-none focus:ring-2"
              style={{
                backgroundColor: "var(--bg-secondary)",
                color: "var(--text-primary)",
                focusRingColor: "var(--brand-primary)"
              }}
            />
            <button
              type="button"
              className="absolute right-3 top-1/2 transform -translate-y-1/2 transition-colors"
              style={{ color: "var(--text-muted)" }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = "var(--text-secondary)";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = "var(--text-muted)";
              }}
              aria-label="Search settings"
              title="Search settings"
            >
              <Settings size={20} />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex flex-col ">
          <div className="p-4 space-y-3">
            {/* Create Community */}
            <Link
              href="/communityform"
              className="flex items-center gap-3 p-3 transition-colors duration-200 rounded-lg"
              style={{
                color: "var(--text-primary)"
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = "var(--hover-bg)";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = "transparent";
              }}
              onClick={onClose}
            >
              <div 
                className="w-8 h-8 rounded-lg flex items-center justify-center"
                style={{ backgroundColor: "var(--bg-secondary)" }}
              >
                <Plus size={18} style={{ color: "var(--brand-primary)" }} />
              </div>
              <span className="font-medium">Create a community</span>
            </Link>

            {/* Discover Communities */}
            <Link
              href="/community-feed"
              className="flex items-center gap-3 p-3 transition-colors duration-200 rounded-lg"
              style={{
                color: "var(--text-primary)"
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = "var(--hover-bg)";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = "transparent";
              }}
              onClick={onClose}
            >
              <div 
                className="w-8 h-8 rounded-lg flex items-center justify-center"
                style={{ backgroundColor: "var(--bg-secondary)" }}
              >
                <Compass size={18} style={{ color: "var(--brand-primary)" }} />
              </div>
              <span className="font-medium">Discover communities</span>
            </Link>

            {/* User Communities */}
            {userCommunities && userCommunities.length > 0 ? (
              <div className="space-y-2 mt-6">
                <div 
                  className="text-xs px-3 mb-2"
                  style={{ color: "var(--text-muted)" }}
                >
                  {userCommunities.length} communities
                </div>
                <ul className="space-">
                  {userCommunities
                    .filter(community =>
                      searchTerm === "" ||
                      community.name.toLowerCase().includes(searchTerm.toLowerCase())
                    )
                    .map((community) => {
                      const isCurrentCommunity = currentCommunity?.slug === community.slug;
                      return (
                        <li key={community._id}>
                          <Link
                            href={`/Newcompage/${community.slug}`}
                            className={`flex items-center gap-3 p-3 transition-colors duration-200 rounded-lg w-full ${
                              isCurrentCommunity ? "bg-primary" : "hover:bg-base-200"
                            }`}
                            onClick={onClose}
                          >
                            <CommunityIcon
                              iconUrl={community.iconImageUrl}
                              name={community.name}
                              size="sm"
                              className="w-8 h-8 rounded-lg"
                              priority={true}
                            />
                            <span className="font-medium truncate">{community.name}</span>
                          </Link>
                        </li>
                      );
                    })}
                </ul>
              </div>
            ) : (
              <div className="text-center py-8">
                <p 
                  className="text-sm"
                  style={{ color: "var(--text-muted)" }}
                >
                  You haven't joined any communities yet
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
